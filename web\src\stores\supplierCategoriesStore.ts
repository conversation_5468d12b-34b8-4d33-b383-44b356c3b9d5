import { create } from 'zustand';

type CategoryStore = {
  categories: string[];
  selectedCategory: string | null;
  addCategory: (cat: string) => void;
  deleteCategory: (cat: string) => void;
  selectCategory: (cat: string | null) => void;
  renameCategory: (oldCat: string, newCat: string) => void;
}

export const useSupplierCategories = create<CategoryStore>((set) => ({
  categories: [],
  selectedCategory: null,
  addCategory: (cat) =>
    set((s) => {
      const trimmedCat = cat.trim();
      // Only add if category doesn't already exist
      if (!s.categories.includes(trimmedCat)) {
        return { categories: [...s.categories, trimmedCat] };
      }
      return s; // Return unchanged state if category already exists
    }),
  deleteCategory: (cat) =>
    set((s) => ({
      categories: s.categories.filter((c) => c !== cat),
      selectedCategory: s.selectedCategory === cat ? null : s.selectedCategory,
    })),
  selectCategory: (cat) => set(() => ({ selectedCategory: cat })),
  renameCategory: (oldCat, newCat) =>
    set((s) => ({
      categories: s.categories.map((c) =>
        c === oldCat ? newCat.trim() : c
      ),
    })),
}));
