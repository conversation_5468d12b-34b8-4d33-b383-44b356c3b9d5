import { create } from 'zustand';

type Addition = {
  id: string;
  name: string;
  price: number;
};

type Product = {
  id: string;
  name: string;
  image: string;
  price: number;
  category: string;
  discountPrice?: number;
  restaurantOptions?: {
    additions?: Addition[];
    without?: string[];
    sides?: Addition[];
  };
  clothingOptions?: {
    sizes: string[];
    colors: string[];
    gallery: string[];
  };
  customOptions?: {
    id: string;
    title: string;
    type: 'text' | 'number' | 'select' | 'multi-select';
    values?: string[]; // for select types
  }[];
};

type SupplierProductsStore = {
  products: Product[];
  setProducts: (products: Product[]) => void;
  addProduct: (product: Product) => void;
  updateProduct: (id: string, updated: Partial<Product>) => void;
  deleteProduct: (id: string) => void;
  saveProducts: () => Promise<void>;
  lastSaved: Date | null;
  isSaving: boolean;
};

export const useSupplierProducts = create<SupplierProductsStore>((set, get) => ({
  products: [],
  lastSaved: null,
  isSaving: false,

  setProducts: (products) => set({ products }),

  addProduct: (product) => set((state) => ({ products: [product, ...state.products] })),

  updateProduct: (id, updated) =>
    set((state) => ({
      products: state.products.map((p) => (p.id === id ? { ...p, ...updated } : p)),
    })),

  deleteProduct: (id) =>
    set((state) => ({
      products: state.products.filter((p) => p.id !== id),
    })),

  saveProducts: async () => {
    set({ isSaving: true });

    try {
      const { products } = get();

      // Simulate API call to save products
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Here you would make the actual API call
      console.log('Saving products to backend:', products);

      // For now, just save to localStorage as a backup
      localStorage.setItem('supplier-products', JSON.stringify({
        products,
        savedAt: new Date().toISOString(),
        totalProducts: products.length,
        totalOptions: products.reduce((total, product) => {
          let count = 0;

          // Count restaurant options
          if (product.restaurantOptions) {
            count += (product.restaurantOptions.additions?.length || 0);
            count += (product.restaurantOptions.without?.length || 0);
            count += (product.restaurantOptions.sides?.length || 0);
          }

          // Count clothing options
          if (product.clothingOptions) {
            count += (product.clothingOptions.sizes?.length || 0);
            count += (product.clothingOptions.colors?.length || 0);
            count += (product.clothingOptions.gallery?.length || 0);
          }

          // Count custom options
          if (product.customOptions) {
            count += product.customOptions.length;
          }

          return total + count;
        }, 0)
      }));

      set({
        lastSaved: new Date(),
        isSaving: false
      });

      return Promise.resolve();

    } catch (error) {
      set({ isSaving: false });
      throw error;
    }
  }
}));

export type { Product, Addition };
