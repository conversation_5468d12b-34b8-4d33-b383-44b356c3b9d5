import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  Plus,
  Save,
  Loader,
  CheckCircle2,
  AlertCircle,
  X,
  Upload,
  Camera,
  Image as ImageIcon,
  Tag,
  DollarSign,
  FileText,
  Trash2,
  ChevronDown,
  Rocket,
  ArrowRight,
  Sparkles,
  Crown,
  Target,
  Award
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useSupplierProducts, type Product } from '../../../stores/supplierProductsStore';
import { useSupplierCategories } from '../../../stores/supplierCategoriesStore';
import { v4 as uuidv4 } from 'uuid';

// Modern Glass Card Component - Same as supplier home
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className}`}
    style={{
      zIndex: 10,
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Simple Background Orb Component - Same as supplier home
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06,
      zIndex: -1,
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Simple Particle System Component
const ParticleSystem: React.FC = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: -2 }}>
    {Array.from({ length: 10 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full opacity-10"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          pointerEvents: 'none',
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 0.1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          delay: Math.random() * 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

// Enhanced Modal Component
const Modal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  gradient?: string;
}> = ({ isOpen, onClose, title, children, gradient = 'from-purple-600 to-blue-600' }) => (
  <AnimatePresence>
    {isOpen && (
      <>
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 50 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
        >
          <div className="relative bg-white rounded-3xl shadow-2xl max-w-md w-full overflow-hidden">
            {/* Header */}
            <div className={`bg-gradient-to-r ${gradient} p-6`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-xl border border-white/30">
                    <AlertCircle size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-white text-xl font-bold">{title}</h3>
                  </div>
                </div>

                <motion.button
                  onClick={onClose}
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-2 bg-white/20 rounded-xl border border-white/30 hover:bg-white/30 transition-colors"
                >
                  <X size={20} className="text-white" />
                </motion.button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {children}
            </div>
          </div>
        </motion.div>
      </>
    )}
  </AnimatePresence>
);

const AddProductPage: React.FC = () => {
  const navigate = useNavigate();
  const { addProduct } = useSupplierProducts();
  const { categories } = useSupplierCategories();

  // Enhanced state management
  const [product, setProduct] = useState<Omit<Product, 'id'> & { id?: string }>({
    id: uuidv4(),
    name: "",
    price: 0,
    discountPrice: 0,
    image: "",
    category: "",
    description: "",
    restaurantOptions: {
      additions: [],
      without: [],
      sides: [],
    },
    clothingOptions: {
      sizes: [],
      colors: [],
      gallery: [],
    },
    customOptions: [],
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [selectOpen, setSelectOpen] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // Form validation
  const validateField = useCallback((field: string, value: any) => {
    const newErrors = { ...errors };

    switch (field) {
      case 'name':
        if (!value || value.trim().length < 2) {
          newErrors.name = 'Product name must be at least 2 characters';
        } else if (value.trim().length > 50) {
          newErrors.name = 'Product name must be less than 50 characters';
        } else {
          delete newErrors.name;
        }
        break;
      case 'price':
        if (!value || isNaN(value) || value <= 0) {
          newErrors.price = 'Please enter a valid price';
        } else if (value > 10000) {
          newErrors.price = 'Price seems too high (max: 10,000)';
        } else {
          delete newErrors.price;
        }
        break;
      case 'category':
        if (!value) {
          newErrors.category = 'Please select a category';
        } else {
          delete newErrors.category;
        }
        break;
      case 'image':
        if (!value) {
          newErrors.image = 'Please upload a product image';
        } else {
          delete newErrors.image;
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [errors]);

  // Handle image upload (web version)
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setImageLoading(true);
    const file = event.target.files?.[0];

    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        setProduct({ ...product, image: imageUrl });
        validateField('image', imageUrl);
        setImageLoading(false);
      };
      reader.readAsDataURL(file);
    } else {
      setImageLoading(false);
    }
  };

  const validateAndContinue = async () => {
    // Validate all fields
    const nameValid = validateField('name', product.name);
    const priceValid = validateField('price', product.price);
    const categoryValid = validateField('category', product.category);
    const imageValid = validateField('image', product.image);

    if (!nameValid || !priceValid || !categoryValid || !imageValid) {
      setShowSuccessModal(true);
      return;
    }

    setIsLoading(true);

    try {
      // Add product to global list
      addProduct(product as Product);

      // Show success and navigate back to products page for now
      // TODO: Create manage-options page later
      setTimeout(() => {
        setIsLoading(false);
        navigate('/supplier/products');
      }, 2000);

    } catch (error) {
      console.error('Failed to create product:', error);
      setIsLoading(false);
    }
  };

  // Calculate form completion percentage
  const getCompletionPercentage = () => {
    let completed = 0;
    const total = 4; // name, price, category, image

    if (product.name.trim()) completed++;
    if (product.price > 0) completed++;
    if (product.category) completed++;
    if (product.image) completed++;

    return Math.round((completed / total) * 100);
  };

  const filteredCategories = categories.filter((cat) => cat !== "All");

  // Set default category to first available (excluding 'All')
  useEffect(() => {
    if (!product.category && filteredCategories.length > 0) {
      setProduct((prev) => ({ ...prev, category: filteredCategories[0] }));
    }
  }, [filteredCategories.length, product.category]);

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.08); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(30px, -25px) rotate(2deg); }
          50% { transform: translate(-20px, 20px) rotate(-2deg); }
          75% { transform: translate(25px, 15px) rotate(1deg); }
        }
      `}</style>

      {/* Success Modal */}
      <Modal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Validation Error"
        gradient="from-red-600 to-pink-600"
      >
        <div className="space-y-4">
          <p className="text-gray-700">
            Please fix the errors before continuing:
          </p>
          <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
            {Object.values(errors).map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
          <button
            onClick={() => setShowSuccessModal(false)}
            className="w-full px-4 py-3 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-colors font-semibold"
          >
            Got it
          </button>
        </div>
      </Modal>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Same as supplier home */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Particle System */}
          <ParticleSystem />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Main Content Container */}
        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <div className="max-w-4xl mx-auto space-y-10">

            {/* Enhanced Header with Progress */}
            <motion.div
              initial={{ opacity: 0, y: -30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <GlassCard gradient="from-purple-600/25 to-blue-600/25" className="p-10">
                <div className="space-y-6">
                  {/* Header Content */}
                  <div className="flex items-center gap-6">
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ delay: 0.2, type: 'spring', damping: 15 }}
                      className="relative"
                    >
                      <div className="p-6 bg-white/25 border border-white/40 rounded-3xl">
                        <Plus size={32} className="text-white" />
                      </div>
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-purple-400/40 to-blue-400/40 rounded-3xl blur-2xl"
                        animate={{ opacity: [0.3, 0.6, 0.3] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    </motion.div>

                    <div className="flex-1">
                      <motion.h1
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                        className="text-white text-4xl font-black mb-3 bg-gradient-to-r from-white via-yellow-200 to-orange-200 bg-clip-text text-transparent"
                      >
                        Create New Product
                        <motion.span
                          animate={{ rotate: [0, 20, -20, 0] }}
                          transition={{ duration: 1.5, repeat: Infinity, delay: 1 }}
                          className="inline-block ml-2"
                        >
                          ✨
                        </motion.span>
                      </motion.h1>
                      <motion.p
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                        className="text-white/90 text-lg"
                      >
                        Build your product step by step
                      </motion.p>
                    </div>
                  </div>

                  {/* Progress Section */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-white/80 text-sm font-semibold">
                        Form Progress
                      </span>
                      <span className="text-white text-sm font-bold">
                        {getCompletionPercentage()}% Complete
                      </span>
                    </div>

                    {/* Step Indicators */}
                    <div className="flex justify-between items-center">
                      {[
                        { icon: Tag, label: 'Name', completed: !!product.name.trim() },
                        { icon: DollarSign, label: 'Price', completed: product.price > 0 },
                        { icon: Package, label: 'Category', completed: !!product.category },
                        { icon: ImageIcon, label: 'Image', completed: !!product.image },
                      ].map((step, index) => (
                        <div key={index} className="flex flex-col items-center gap-2 relative">
                          <motion.div
                            className={`p-3 rounded-2xl border-2 transition-all duration-300 relative z-10 ${
                              step.completed
                                ? 'bg-white border-white text-purple-600'
                                : 'bg-white/20 border-white/30 text-white'
                            }`}
                            whileHover={{ scale: 1.1 }}
                            animate={step.completed ? { scale: [1, 1.2, 1] } : {}}
                            transition={{ duration: 0.3 }}
                          >
                            <step.icon size={20} />
                          </motion.div>
                          <span
                            className={`text-sm font-semibold transition-all duration-300 ${
                              step.completed ? 'text-white' : 'text-white/70'
                            }`}
                          >
                            {step.label}
                          </span>
                        </div>
                      ))}
                    </div>

                    {/* Progress Bar - positioned to align with step indicators */}
                    <div className="relative">
                      <div className="absolute top-0 left-0 right-0 flex justify-between items-center pointer-events-none">
                        {[0, 1, 2, 3].map((stepIndex) => (
                          <div key={stepIndex} className="w-12 h-3 flex justify-center">
                            <div className="w-1 h-3 bg-white/20 rounded-full" />
                          </div>
                        ))}
                      </div>

                      <div className="w-full bg-white/20 rounded-full h-3 overflow-hidden relative">
                        <motion.div
                          className="h-full bg-gradient-to-r from-white to-yellow-200 rounded-full relative"
                          initial={{ width: 0 }}
                          animate={{
                            width: `${Math.min(getCompletionPercentage(), 100)}%`
                          }}
                          transition={{ duration: 0.5, ease: "easeOut" }}
                        />

                        {/* Progress markers aligned with step indicators */}
                        <div className="absolute top-0 left-0 right-0 h-full flex justify-between items-center pointer-events-none">
                          {[
                            { completed: !!product.name.trim(), position: '12.5%' },
                            { completed: product.price > 0, position: '37.5%' },
                            { completed: !!product.category, position: '62.5%' },
                            { completed: !!product.image, position: '87.5%' },
                          ].map((marker, index) => (
                            <motion.div
                              key={index}
                              className={`w-4 h-4 rounded-full border-2 transition-all duration-300 ${
                                marker.completed
                                  ? 'bg-white border-white shadow-lg'
                                  : 'bg-white/20 border-white/40'
                              }`}
                              style={{
                                position: 'absolute',
                                left: marker.position,
                                transform: 'translateX(-50%)',
                                top: '50%',
                                marginTop: '-8px'
                              }}
                              animate={marker.completed ? {
                                scale: [1, 1.3, 1],
                                boxShadow: [
                                  '0 0 0 0 rgba(255, 255, 255, 0.4)',
                                  '0 0 0 8px rgba(255, 255, 255, 0)',
                                  '0 0 0 0 rgba(255, 255, 255, 0)'
                                ]
                              } : {}}
                              transition={{ duration: 0.6 }}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Enhanced Form Card */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <GlassCard gradient="from-white/25 to-white/15" className="p-10">
                <div className="space-y-8">

                  {/* Product Name Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-purple-500/20 rounded-xl border border-purple-400/30">
                        <Tag size={20} className="text-purple-400" />
                      </div>
                      <h3 className="text-white text-xl font-bold">Product Name</h3>
                      <span className="text-red-400 text-lg">*</span>
                    </div>

                    <input
                      type="text"
                      placeholder="e.g., Delicious Chicken Burger"
                      value={product.name}
                      onChange={(e) => {
                        setProduct({ ...product, name: e.target.value });
                        validateField('name', e.target.value);
                      }}
                      className={`w-full px-6 py-4 bg-white/10 border-2 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:border-purple-400 transition-all duration-300 text-lg ${
                        errors.name ? 'border-red-400' : 'border-white/30'
                      }`}
                    />

                    {errors.name && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center gap-2 text-red-400"
                      >
                        <AlertCircle size={16} />
                        <span className="text-sm">{errors.name}</span>
                      </motion.div>
                    )}

                    <p className="text-white/70 text-sm">
                      Choose a descriptive name that customers will love
                    </p>
                  </div>

                  {/* Category Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-blue-500/20 rounded-xl border border-blue-400/30">
                        <Package size={20} className="text-blue-400" />
                      </div>
                      <h3 className="text-white text-xl font-bold">Category</h3>
                      <span className="text-red-400 text-lg">*</span>
                    </div>

                    <div className="relative">
                      <button
                        onClick={() => setSelectOpen(!selectOpen)}
                        className={`w-full px-6 py-4 bg-white/10 border-2 rounded-2xl text-white focus:outline-none focus:border-blue-400 transition-all duration-300 text-lg flex items-center justify-between ${
                          errors.category ? 'border-red-400' : 'border-white/30'
                        }`}
                      >
                        <span className={product.category ? 'text-white' : 'text-white/60'}>
                          {product.category || "Select a category"}
                        </span>
                        <motion.div
                          animate={{ rotate: selectOpen ? 180 : 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <ChevronDown size={20} />
                        </motion.div>
                      </button>

                      <AnimatePresence>
                        {selectOpen && (
                          <motion.div
                            initial={{ opacity: 0, y: -10, scale: 0.97 }}
                            animate={{ opacity: 1, y: 0, scale: 1 }}
                            exit={{ opacity: 0, y: -10, scale: 0.97 }}
                            transition={{ duration: 0.2 }}
                            className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-sm border border-white/30 rounded-2xl shadow-2xl z-20 overflow-hidden"
                          >
                            {filteredCategories.map((cat, idx) => {
                              const selected = product.category === cat;
                              return (
                                <motion.button
                                  key={cat}
                                  onClick={() => {
                                    setProduct({ ...product, category: cat });
                                    validateField('category', cat);
                                    setSelectOpen(false);
                                  }}
                                  whileHover={{ backgroundColor: selected ? '#7c3aed' : '#f3f4f6' }}
                                  className={`w-full px-6 py-4 text-left transition-all duration-200 ${
                                    selected
                                      ? 'bg-purple-600 text-white'
                                      : 'text-gray-800 hover:bg-gray-100'
                                  } ${idx === 0 ? 'rounded-t-2xl' : ''} ${idx === filteredCategories.length - 1 ? 'rounded-b-2xl' : ''}`}
                                >
                                  <div className="flex items-center justify-between">
                                    <span className="font-semibold">{cat}</span>
                                    {selected && (
                                      <CheckCircle2 size={18} className="text-white" />
                                    )}
                                  </div>
                                </motion.button>
                              );
                            })}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>

                    {errors.category && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center gap-2 text-red-400"
                      >
                        <AlertCircle size={16} />
                        <span className="text-sm">{errors.category}</span>
                      </motion.div>
                    )}

                    <p className="text-white/70 text-sm">
                      Choose the category that best fits your product
                    </p>
                  </div>

                  {/* Price Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-green-500/20 rounded-xl border border-green-400/30">
                        <DollarSign size={20} className="text-green-400" />
                      </div>
                      <h3 className="text-white text-xl font-bold">Price</h3>
                      <span className="text-red-400 text-lg">*</span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="text-white/80 text-sm font-semibold">Regular Price (₪)</label>
                        <input
                          type="number"
                          placeholder="e.g., 25.00"
                          value={product.price || ''}
                          onChange={(e) => {
                            const price = parseFloat(e.target.value) || 0;
                            setProduct({ ...product, price });
                            validateField('price', price);
                          }}
                          className={`w-full px-6 py-4 bg-white/10 border-2 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:border-green-400 transition-all duration-300 text-lg ${
                            errors.price ? 'border-red-400' : 'border-white/30'
                          }`}
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-white/80 text-sm font-semibold">Discount Price (₪)</label>
                        <input
                          type="number"
                          placeholder="Optional"
                          value={product.discountPrice || ''}
                          onChange={(e) => {
                            const discountPrice = parseFloat(e.target.value) || 0;
                            setProduct({ ...product, discountPrice });
                          }}
                          className="w-full px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:border-green-400 transition-all duration-300 text-lg"
                        />
                      </div>
                    </div>

                    {errors.price && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center gap-2 text-red-400"
                      >
                        <AlertCircle size={16} />
                        <span className="text-sm">{errors.price}</span>
                      </motion.div>
                    )}

                    <p className="text-white/70 text-sm">
                      Set competitive pricing. Discount price is optional for promotions.
                    </p>
                  </div>

                  {/* Enhanced Image Upload Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-orange-500/20 rounded-xl border border-orange-400/30">
                        <ImageIcon size={20} className="text-orange-400" />
                      </div>
                      <h3 className="text-white text-xl font-bold">Product Image</h3>
                      <span className="text-red-400 text-lg">*</span>
                    </div>

                    {!product.image ? (
                      <div className={`relative border-2 border-dashed rounded-3xl p-12 text-center transition-all duration-300 ${
                        errors.image ? 'border-red-400 bg-red-500/10' : 'border-white/30 bg-white/5 hover:border-white/50 hover:bg-white/10'
                      }`}>
                        <div className="space-y-6">
                          <motion.div
                            className="mx-auto w-20 h-20 bg-gradient-to-r from-purple-600 to-blue-600 rounded-3xl flex items-center justify-center"
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 300, damping: 20 }}
                          >
                            <Upload size={32} className="text-white" />
                          </motion.div>

                          <div>
                            <h4 className="text-white text-xl font-bold mb-2">Upload Product Image</h4>
                            <p className="text-white/70 text-sm">
                              Choose a high-quality image that showcases your product
                            </p>
                          </div>

                          <div className="flex gap-4 justify-center">
                            <motion.label
                              whileHover={{ scale: 1.05, y: -2 }}
                              whileTap={{ scale: 0.95 }}
                              className="cursor-pointer px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl border border-white/30 hover:from-purple-700 hover:to-blue-700 transition-all duration-300 flex items-center gap-3 font-bold shadow-xl"
                            >
                              {imageLoading ? (
                                <Loader size={20} className="animate-spin" />
                              ) : (
                                <Upload size={20} />
                              )}
                              {imageLoading ? 'Uploading...' : 'Choose File'}
                              <input
                                type="file"
                                accept="image/*"
                                onChange={handleImageUpload}
                                className="hidden"
                                disabled={imageLoading}
                              />
                            </motion.label>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="relative bg-white/10 rounded-3xl p-6 border border-white/30">
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-full h-64 object-cover rounded-2xl"
                          />
                        </div>

                        <div className="flex gap-4">
                          <motion.label
                            whileHover={{ scale: 1.05, y: -2 }}
                            whileTap={{ scale: 0.95 }}
                            className="cursor-pointer flex-1 px-6 py-3 bg-white/15 text-white rounded-2xl border border-white/30 hover:bg-white/25 transition-all duration-300 flex items-center justify-center gap-3 font-bold"
                          >
                            <Upload size={18} />
                            Change Image
                            <input
                              type="file"
                              accept="image/*"
                              onChange={handleImageUpload}
                              className="hidden"
                              disabled={imageLoading}
                            />
                          </motion.label>

                          <motion.button
                            whileHover={{ scale: 1.05, y: -2 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => {
                              setProduct({ ...product, image: '' });
                              validateField('image', '');
                            }}
                            className="flex-1 px-6 py-3 bg-gradient-to-r from-red-500/30 to-pink-500/30 text-red-300 rounded-2xl border border-red-400/40 hover:from-red-500/40 hover:to-pink-500/40 transition-all duration-300 flex items-center justify-center gap-3 font-bold"
                          >
                            <Trash2 size={18} />
                            Remove
                          </motion.button>
                        </div>
                      </div>
                    )}

                    {errors.image && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center gap-2 text-red-400"
                      >
                        <AlertCircle size={16} />
                        <span className="text-sm">{errors.image}</span>
                      </motion.div>
                    )}

                    <p className="text-white/70 text-sm">
                      High-quality images help customers make better decisions
                    </p>
                  </div>

                  {/* Optional Description Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-indigo-500/20 rounded-xl border border-indigo-400/30">
                        <FileText size={20} className="text-indigo-400" />
                      </div>
                      <h3 className="text-white text-xl font-bold">Description</h3>
                      <span className="text-white/60 text-sm">(Optional)</span>
                    </div>

                    <textarea
                      placeholder="Describe your product (optional)"
                      value={product.description || ''}
                      onChange={(e) => setProduct({ ...product, description: e.target.value })}
                      rows={4}
                      className="w-full px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:border-indigo-400 transition-all duration-300 text-lg resize-none"
                    />

                    <p className="text-white/70 text-sm">
                      Add details about ingredients, materials, or special features
                    </p>
                  </div>

                </div>
              </GlassCard>
            </motion.div>

            {/* Enhanced Submit Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              <GlassCard gradient="from-purple-600/25 to-blue-600/25" className="p-8" hoverEffect={false}>
                <motion.button
                  onClick={validateAndContinue}
                  disabled={isLoading || getCompletionPercentage() < 100}
                  whileHover={getCompletionPercentage() === 100 ? {
                    scale: 1.02,
                    y: -5,
                    boxShadow: "0 20px 40px -12px rgba(139, 92, 246, 0.4)"
                  } : {}}
                  whileTap={{ scale: 0.98 }}
                  className={`w-full p-8 rounded-3xl border-2 transition-all duration-300 flex items-center justify-center gap-6 font-bold text-xl ${
                    getCompletionPercentage() === 100
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 border-white/30 text-white hover:from-purple-700 hover:to-blue-700 shadow-2xl'
                      : 'bg-white/10 border-white/20 text-white/50 cursor-not-allowed'
                  }`}
                >
                  <div className="flex items-center gap-6">
                    {isLoading ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Loader size={28} className="text-white" />
                      </motion.div>
                    ) : (
                      <motion.div
                        whileHover={{ scale: 1.2, rotate: 10 }}
                        transition={{ type: "spring", stiffness: 400, damping: 15 }}
                      >
                        <Rocket size={28} />
                      </motion.div>
                    )}

                    <div className="text-center">
                      <div className="text-2xl font-black">
                        {isLoading ? 'Creating Product...' : 'Create Product & Add Options'}
                      </div>
                      {getCompletionPercentage() < 100 ? (
                        <div className="text-sm opacity-80 mt-1">
                          Complete {100 - getCompletionPercentage()}% more to continue
                        </div>
                      ) : (
                        <div className="text-sm opacity-80 mt-1">
                          Ready to create your amazing product!
                        </div>
                      )}
                    </div>

                    {!isLoading && getCompletionPercentage() === 100 && (
                      <motion.div
                        whileHover={{ x: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 15 }}
                      >
                        <ArrowRight size={28} />
                      </motion.div>
                    )}
                  </div>
                </motion.button>
              </GlassCard>
            </motion.div>

          </div>
        </div>
      </div>
    </>
  );
};

export default AddProductPage;
